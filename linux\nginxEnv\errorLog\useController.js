import { ref } from 'vue';

// 页面引用
export const pageContainer = ref(null);

// 日志内容
export const logContent = ref('');

// 初始化错误日志数据
export const initErrorLogData = async () => {
	try {
		await loadLogContent();
	} catch (error) {
		console.error('初始化错误日志数据失败:', error);
	}
};

// 加载日志内容
const loadLogContent = async () => {
	try {
		// 模拟API调用获取日志内容
		const content = await mockGetErrorLogContent();
		logContent.value = content;
	} catch (error) {
		console.error('加载日志内容失败:', error);
	}
};

// 模拟API - 获取错误日志内容
const mockGetErrorLogContent = async () => {
	return new Promise((resolve) => {
		setTimeout(() => {
			// 模拟从您提供的图片中的日志内容
			const logLines = [
				'2025/06/18 17:00:17 [emerg] 219268240: bind() to 0.0.0.0:5465 failed (98: Address already in use)',
				'2025/06/18 17:00:17 [emerg] 219268240: bind() to 0.0.0.0:123 failed (98: Address already in use)',
				'2025/06/18 17:00:17 [emerg] 219268240: bind() to 0.0.0.0:888 failed (98: Address already in use)',
				'2025/06/18 17:00:17 [emerg] 219268240: bind() to 0.0.0.0:880 failed (98: Address already in use)',
				'2025/06/18 17:00:17 [emerg] 219268240: bind() to 0.0.0.0:24425 failed (98: Address already in use)',
				'2025/06/18 17:00:17 [emerg] 219268240: bind() to 0.0.0.0:443 failed (98: Address already in use)',
				'2025/06/18 17:00:17 [emerg] 219268240: bind() to 0.0.0.0:2515 failed (98: Address already in use)',
				'2025/06/18 17:00:17 [emerg] 219268240: bind() to 0.0.0.0:5465 failed (98: Address already in use)',
				'2025/06/18 17:00:17 [emerg] 219268240: bind() to 0.0.0.0:123 failed (98: Address already in use)',
				'2025/06/18 17:00:17 [emerg] 219268240: bind() to 0.0.0.0:888 failed (98: Address already in use)',
				'2025/06/18 17:00:17 [emerg] 219268240: bind() to 0.0.0.0:880 failed (98: Address already in use)',
				'2025/06/18 17:00:17 [emerg] 219268240: bind() to 0.0.0.0:24425 failed (98: Address already in use)',
				'2025/06/18 17:00:17 [emerg] 219268240: bind() to 0.0.0.0:443 failed (98: Address already in use)',
				'2025/06/18 17:00:17 [emerg] 219268240: bind() to 0.0.0.0:2515 failed (98: Address already in use)',
				'2025/06/18 17:00:17 [emerg] 219268240: bind() to 0.0.0.0:5465 failed (98: Address already in use)',
				'2025/06/18 17:00:17 [emerg] 219268240: bind() to 0.0.0.0:123 failed (98: Address already in use)',
				'2025/06/18 17:00:17 [emerg] 219268240: bind() to 0.0.0.0:888 failed (98: Address already in use)',
				'2025/06/18 17:00:17 [emerg] 219268240: bind() to 0.0.0.0:880 failed (98: Address already in use)',
				'2025/06/18 17:00:17 [emerg] 219268240: still could not bind()',
				'2025/06/18 17:00:17 [emerg] 219268240: bind() to 0.0.0.0:443 failed (98: Address already in use)',
				'2025/06/18 17:00:17 [emerg] 219268240: bind() to 0.0.0.0:2515 failed (98: Address already in use)',
				'2025/06/18 17:00:17 [emerg] 219268240: bind() to 0.0.0.0:5465 failed (98: Address already in use)',
				'2025/06/18 17:00:17 [emerg] 219268240: bind() to 0.0.0.0:123 failed (98: Address already in use)',
				'2025/06/18 17:00:17 [emerg] 219268240: bind() to 0.0.0.0:888 failed (98: Address already in use)',
				'2025/06/18 17:00:17 [emerg] 219268240: bind() to 0.0.0.0:880 failed (98: Address already in use)',
				'2025/06/18 17:00:17 [emerg] 219268240: still could not bind()',
			];

			resolve(logLines.join('\n'));
		}, 300);
	});
};

// 刷新日志
export const refreshLogs = async () => {
	try {
		pageContainer.value?.notify?.info('正在刷新日志...');
		await loadLogContent();
		pageContainer.value?.notify?.success('日志刷新成功');
	} catch (error) {
		console.error('刷新日志失败:', error);
		pageContainer.value?.notify?.error('刷新失败，请重试');
	}
};
