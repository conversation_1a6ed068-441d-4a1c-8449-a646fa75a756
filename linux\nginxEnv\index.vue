<template>
	<page-container ref="pageContainer" :is-back="true" title="Nginx环境管理">
		<view class="nginx-env-container">
			<!-- 服务状态卡片 -->
			<view class="status-card">
				<view class="status-header">
					<view class="status-info">
						<text class="status-title">Nginx服务</text>
						<text class="status-desc">当前运行状态</text>
					</view>
					<view class="status-control">
						<text class="status-badge" :class="serviceStatus.isRunning ? 'badge-success' : 'badge-danger'">
							{{ serviceStatus.isRunning ? '运行中' : '已停止' }}
						</text>
						<uv-switch
							:model-value="serviceStatus.isRunning"
							size="24"
							activeColor="#20a50a"
							:loading="serviceLoading"
							@change="toggleService"
						></uv-switch>
					</view>
				</view>

				<!-- 快速操作按钮 -->
				<view class="quick-actions">
					<view
						v-for="(action, index) in quickActions"
						:key="index"
						class="action-btn"
						:class="{
							'start-btn': action.id === 'start',
							'stop-btn': action.id === 'stop'
						}"
						@tap="handleQuickAction(action)"
						hover-class="action-hover"
					>
						<uv-icon
							:name="action.icon"
							size="24"
							:color="action.id === 'stop' ? '#dc2626' : '#20a50a'"
						></uv-icon>
						<text class="action-text">{{ action.title }}</text>
					</view>
				</view>
			</view>

			<!-- 功能模块 -->
			<view class="function-modules">
				<view
					v-for="(module, index) in functionModules"
					:key="index"
					class="module-card"
					@tap="handleModuleClick(module)"
					hover-class="module-hover"
				>
					<view class="module-header">
						<view class="module-icon">
							<uv-icon :name="module.icon" size="24" color="#20a50a"></uv-icon>
						</view>
						<text class="module-title">{{ module.title }}</text>
					</view>
					<text class="module-desc">{{ module.desc }}</text>
					<view v-if="module.showData" class="module-data">
						<text class="data-value">{{ module.dataValue }}</text>
						<text class="data-unit">{{ module.dataUnit }}</text>
					</view>
				</view>
			</view>


		</view>


	</page-container>
</template>

<script setup>
	import PageContainer from '@/components/PageContainer/index.vue';
	import { onShow } from '@dcloudio/uni-app';
	import {
		pageContainer,
		serviceStatus,
		serviceLoading,
		functionModules,
		quickActions,
		toggleService,
		handleModuleClick,
		handleQuickAction,
		initNginxEnvData
	} from './useController.js';

	onShow(async () => {
		await initNginxEnvData();
	});
</script>

<style lang="scss" scoped>
	.nginx-env-container {
		padding: 24rpx;
		display: flex;
		flex-direction: column;
		gap: 24rpx;
		background-color: var(--page-bg-color);
	}

	/* 服务状态卡片 */
	.status-card {
		background-color: var(--dialog-bg-color);
		border-radius: 20rpx;
		padding: 32rpx;
		box-shadow: 0 6rpx 20rpx rgba(0, 0, 0, 0.1);
		border: 2rpx solid rgba(32, 165, 10, 0.1);
	}

	.status-header {
		display: flex;
		justify-content: space-between;
		align-items: center;
		margin-bottom: 24rpx;
	}

	.status-info {
		.status-title {
			font-size: 32rpx;
			font-weight: 600;
			color: var(--text-primary-color);
			display: block;
		}

		.status-desc {
			font-size: 24rpx;
			color: var(--text-secondary-color);
			display: block;
			margin-top: 8rpx;
		}
	}

	.status-control {
		display: flex;
		align-items: center;
		gap: 24rpx;
	}

	.status-badge {
		font-size: 24rpx;
		font-weight: 500;
		padding: 8rpx 16rpx;
		border-radius: 20rpx;

		&.badge-success {
			background-color: #dcfce7;
			color: #16a34a;
		}

		&.badge-danger {
			background-color: #fee2e2;
			color: #dc2626;
		}
	}

	/* 快速操作按钮 */
	.quick-actions {
		display: flex;
		justify-content: space-around;
		gap: 24rpx;
		margin-top: 24rpx;
		padding-top: 24rpx;
		border-top: 2rpx solid var(--border-color);
	}

	.action-btn {
		display: flex;
		flex-direction: row;
		align-items: center;
		justify-content: center;
		padding: 20rpx 16rpx;
		border-radius: 12rpx;
		background-color: transparent;
		border: 2rpx solid rgba(32, 165, 10, 0.3);
		transition: all 0.2s ease;
		flex: 1;
		gap: 12rpx;

		.action-text {
			font-size: 24rpx;
			color: var(--text-primary-color);
			font-weight: 500;
		}
	}

	.action-hover {
		background-color: rgba(32, 165, 10, 0.1);
		border-color: rgba(32, 165, 10, 0.6);
		transform: translateY(-2rpx);
	}

	/* 启动按钮特殊样式 */
	.action-btn.start-btn {
		flex: none;
		width: 200rpx;
		margin: 0 auto;
		border-color: rgba(32, 165, 10, 0.5);
		background-color: rgba(32, 165, 10, 0.05);
	}

	/* 停止按钮特殊样式 */
	.action-btn.stop-btn {
		border-color: rgba(220, 38, 38, 0.3);
		background-color: rgba(220, 38, 38, 0.05);
	}

	.action-btn.stop-btn.action-hover {
		border-color: rgba(220, 38, 38, 0.6);
		background-color: rgba(220, 38, 38, 0.1);
	}

	/* 功能模块 */
	.function-modules {
		display: flex;
		flex-direction: column;
		gap: 16rpx;
	}

	.module-card {
		background-color: var(--dialog-bg-color);
		border-radius: 20rpx;
		padding: 28rpx;
		box-shadow: 0 4rpx 16rpx rgba(0, 0, 0, 0.06);
		border: 2rpx solid rgba(0, 0, 0, 0.05);
		transition: all 0.3s ease;
	}

	.module-hover {
		transform: translateY(-4rpx);
		box-shadow: 0 8rpx 24rpx rgba(0, 0, 0, 0.12);
		border-color: rgba(32, 165, 10, 0.2);
	}

	.module-header {
		display: flex;
		align-items: center;
		gap: 16rpx;
		margin-bottom: 16rpx;

		.module-icon {
			width: 52rpx;
			height: 52rpx;
			display: flex;
			align-items: center;
			justify-content: center;
			background: linear-gradient(135deg, rgba(32, 165, 10, 0.15) 0%, rgba(32, 165, 10, 0.05) 100%);
			border-radius: 16rpx;
			border: 2rpx solid rgba(32, 165, 10, 0.1);


		}

		.module-title {
			font-size: 32rpx;
			font-weight: 600;
			color: var(--text-primary-color);
		}
	}

	.module-desc {
		font-size: 26rpx;
		color: var(--text-secondary-color);
		margin-bottom: 20rpx;
		line-height: 1.5;
	}

	.module-data {
		display: flex;
		align-items: baseline;
		gap: 8rpx;
		padding: 20rpx;
		background: linear-gradient(135deg, rgba(32, 165, 10, 0.05) 0%, rgba(32, 165, 10, 0.02) 100%);
		border-radius: 12rpx;
		border: 2rpx solid rgba(32, 165, 10, 0.1);

		.data-value {
			font-size: 36rpx;
			font-weight: 700;
			color: #20a50a;
		}

		.data-unit {
			font-size: 24rpx;
			color: var(--text-secondary-color);
		}
	}

	/* 响应式调整 */
	@media (max-width: 360px) {
		.function-modules {
			gap: 16rpx;
		}

		.module-card {
			padding: 24rpx;
		}
	}
</style>